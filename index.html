<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to car detailing studio</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <nav class="navbar">
        <div class="nav-container">
          <h1 class="logo">Car Detailing Studio</h1>
          <ul class="nav-menu">
            <li><a href="#home">Home</a></li>
            <li><a href="#about">About</a></li>
            <li><a href="#services">Services</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
      </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-content">
        <div class="hero-image"></div>
        <img
          src="Car_Logo.png"
          height="250px"
          width="250px"
          alt="car logo"
          class="car-logo"
        />
        <h1>Welcome to shree gurukrupa Car Detailing Studio</h1>
        <p>NOTHING IS MORE THAN OUR CUSTOMERS SATISFACTION</p>
        <button class="cta-button">Get Started</button>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
      <div class="container">
        <h2 class="animate-on-scroll">About Us</h2>
        <div class="about-content">
          <div class="about-text animate-slide-left">
            <p>
              At [shree gurukrupa Car Detailing], we're passionate about
              restoring vehicles to their original shine and beyond. Our team
              consists of experienced detailers who are dedicated to delivering
              exceptional results. We use only the highest quality products and
              techniques to ensure your car receives the care it deserves. From
              basic washes to full-scale detailing, we're equipped to handle all
              your car care needs."
            </p>
            <p>
              We understand that your car is more than just a means of
              transportation it's an investment. At [shree gurukrupa Car
              Detailing], we're committed to exceeding your expectations. We
              offer a wide range of detailing services tailored to your specific
              needs and preferences. Our studio is equipped with
              state-of-the-art equipment and utilizes eco-friendly, high-quality
              products. We take pride in our meticulous attention to detail and
              our unwavering commitment to customer satisfaction. Our unique
              approach to car detailing ensures that your vehicle receives the
              care it deserves, leaving it looking like new.
            </p>
          </div>
          <div class="about-image animate-slide-right">
            <div class="placeholder-image">
              <img
                src="car-img.jpeg"
                height="300px"
                width="500px"
                alt="about us"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
      <div class="container">
        <h2 class="animate-on-scroll">Our Services</h2>
        <div class="services-grid">
          <div class="service-card animate-scale animate-delay-1">
            <h3>EXTERIOR DETAILING</h3>
            <img src="" height="300px" width="300px" alt="car spa"" />

            <p>
              Restore your vehicle's shine with our comprehensive exterior
              detailing service, leaving a showroom-quality finish.
            </p>
          </div>
          <div class="service-card animate-scale animate-delay-2">
            <h3>INTERIOR STEAM DETAILING</h3>
            <img
              src="c:\Users\<USER>\OneDrive\Desktop\steam-cleaner-car.jpg"
              height="300px"
              width="300px"
              alt="about us"
            />
            <p>
              Deep clean your car's interior with our eco-friendly steam
              technology, eliminating bacteria and odors without
              harsh chemicals.
            </p>
          </div>
          <div class="service-card animate-scale animate-delay-3">
            <h3>Engine Detailing</h3>
            <p>
              Safely clean your engine bay with our specialized steam
              cleaning process.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
      <div class="container">
        <h2 class="animate-on-scroll">Contact Us</h2>
        <div class="contact-content">
          <div class="contact-info animate-slide-left">
            <h3>Get in Touch</h3>
            <div class="contact-details">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> +91 7600231693</p>
              <p><strong>Address:</strong> manjalpur varsad bridge,vadoda</p>
            </div>
          </div>
          <form class="contact-form animate-slide-right">
            <input type="text" placeholder="Your Name" required />
            <input type="email" placeholder="Your Email" required />
            <textarea placeholder="Your Message" rows="5" required></textarea>
            <button type="submit">Send Message</button>
          </form>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <p>&copy; 2025 MyWebsite. All rights reserved.</p>
      </div>
    </footer>

    <script>
      // Scroll Animation Observer
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate");
          }
        });
      }, observerOptions);

      // Observe all elements with animation classes
      document.addEventListener("DOMContentLoaded", () => {
        const animatedElements = document.querySelectorAll(
          ".animate-on-scroll, .animate-slide-left, .animate-slide-right, .animate-scale, .animate-fade"
        );

        animatedElements.forEach((el) => {
          observer.observe(el);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
              target.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          });
        });

        // Add floating animation to CTA button
        const ctaButton = document.querySelector(".cta-button");
        if (ctaButton) {
          setInterval(() => {
            ctaButton.style.animation = "bounce 2s ease-in-out";
            setTimeout(() => {
              ctaButton.style.animation = "fadeInUp 1s ease 0.4s both";
            }, 2000);
          }, 5000);
        }
      });
    </script>
  </body>
</html>
